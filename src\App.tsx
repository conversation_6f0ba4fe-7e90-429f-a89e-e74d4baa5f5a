import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { PatientsProvider } from "@/context/PatientsContext";
import { AppointmentsProvider } from "@/context/AppointmentsContext";
import { QuestionnairesProvider } from "@/context/QuestionnairesContext";
import { AuthProvider } from "@/context/AuthContext";
import AppLayout from "@/components/layout/AppLayout";
import Dashboard from "@/pages/Dashboard";
import Patients from "@/pages/Patients";
import Login from "@/pages/Login";
import Agenda from "@/pages/Agenda";
import Settings from "@/pages/Settings";
import { Questionnaires } from "@/pages/Questionnaires";
import NovaConsulta from "@/pages/NovaConsulta";
import ProtectedRoute from "@/components/ProtectedRoute";
import NotFound from "@/pages/NotFound";
import { Toaster } from "@/components/ui/toaster";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <PatientsProvider>
            <AppointmentsProvider>
              <QuestionnairesProvider>
          <TooltipProvider>
            <BrowserRouter>
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route path="/" element={
                  <ProtectedRoute>
                    <AppLayout />
                  </ProtectedRoute>
                }>
                  <Route index element={<Dashboard />} />
                  <Route path="nova-consulta" element={<NovaConsulta />} />
                  <Route path="pacientes" element={<Patients />} />
                  <Route path="agenda" element={<Agenda />} />
                  <Route path="questionarios" element={<Questionnaires />} />
                  <Route path="relatorios" element={<div className="p-8 text-center"><h2 className="text-2xl">Relatórios em desenvolvimento</h2></div>} />
                  <Route path="perfil" element={<div className="p-8 text-center"><h2 className="text-2xl">Perfil em desenvolvimento</h2></div>} />
                  <Route path="configuracoes" element={<Settings />} />
                </Route>
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
              <Toaster />
              <Sonner />
            </BrowserRouter>
          </TooltipProvider>
              </QuestionnairesProvider>
            </AppointmentsProvider>
          </PatientsProvider>
        </AuthProvider>
  </QueryClientProvider>
);

export default App;