import { useState } from "react";
import { useAppointments } from "@/context/AppointmentsContext";
import { usePatients } from "@/context/PatientsContext";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar,
  Plus,
  Clock,
  User,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDes<PERSON>,
  <PERSON><PERSON><PERSON>ialog<PERSON><PERSON>er,
  <PERSON>ertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import {
  format,
  addDays,
  subDays,
  startOfWeek,
  addWeeks,
  subWeeks,
} from "date-fns";
import { ptBR } from "date-fns/locale";

const statusColors = {
  scheduled: "bg-blue-500/10 text-blue-700 border-blue-200",
  completed: "bg-green-500/10 text-green-700 border-green-200",
  cancelled: "bg-red-500/10 text-red-700 border-red-200",
  no_show: "bg-gray-500/10 text-gray-700 border-gray-200",
};

const statusLabels = {
  scheduled: "Agendado",
  completed: "Concluído",
  cancelled: "Cancelado",
  no_show: "Falta",
};

export default function Agenda() {
  const {
    appointments,
    addAppointment,
    updateAppointment,
    deleteAppointment,
    loading,
  } = useAppointments();
  const { patients } = usePatients();
  const { toast } = useToast();

  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<"day" | "week">("day");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState(null);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    patient_id: "",
    appointment_date: format(new Date(), "yyyy-MM-dd"),
    start_time: "09:00",
    end_time: "10:00",
    status: "scheduled" as const,
  });

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      patient_id: "",
      appointment_date: format(new Date(), "yyyy-MM-dd"),
      start_time: "09:00",
      end_time: "10:00",
      status: "scheduled",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      toast({
        title: "Erro",
        description: "Título é obrigatório",
        variant: "destructive",
      });
      return;
    }

    try {
      if (editingAppointment) {
        await updateAppointment(editingAppointment.id, formData);
        toast({
          title: "Agendamento atualizado",
          description: "O agendamento foi atualizado com sucesso.",
        });
      } else {
        await addAppointment(formData);
        toast({
          title: "Agendamento criado",
          description: "Novo agendamento foi adicionado com sucesso.",
        });
      }

      setShowAddDialog(false);
      setEditingAppointment(null);
      resetForm();
    } catch (error) {
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar o agendamento.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (appointment) => {
    setEditingAppointment(appointment);
    setFormData({
      title: appointment.title,
      description: appointment.description || "",
      patient_id: appointment.patient_id || "",
      appointment_date: appointment.appointment_date,
      start_time: appointment.start_time,
      end_time: appointment.end_time,
      status: appointment.status,
    });
    setShowAddDialog(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteAppointment(id);
      toast({
        title: "Agendamento excluído",
        description: "O agendamento foi excluído com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao excluir o agendamento.",
        variant: "destructive",
      });
    }
  };

  const getWeekDays = () => {
    const start = startOfWeek(currentDate, { weekStartsOn: 1 });
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getAppointmentsForDate = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    return appointments
      .filter((apt) => apt.appointment_date === dateStr)
      .sort((a, b) => a.start_time.localeCompare(b.start_time));
  };

  const renderWeekView = () => {
    const weekDays = getWeekDays();

    return (
      <div className="flex flex-col gap-4">
        {weekDays.map((day, index) => {
          const dayAppointments = getAppointmentsForDate(day);
          const isToday =
            format(day, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");

          return (
            <Card
              key={index}
              className={`min-h-[100px] ${
                isToday ? "ring-2 ring-primary" : ""
              }`}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-center">
                  <div className="text-xs text-muted-foreground">
                    {format(day, "EEE", { locale: ptBR })}
                  </div>
                  <div
                    className={`text-lg ${
                      isToday ? "text-primary font-bold" : ""
                    }`}
                  >
                    {format(day, "d")}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {dayAppointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="py-2 rounded-md bg-muted/50 border text-xs cursor-pointer hover:bg-muted text-center"
                    onClick={() => handleEdit(appointment)}
                  >
                    <div className="flex flex-col items-center justify-between mb-1">
                      <Badge
                        variant="outline"
                        className={`text-xs ${
                          statusColors[appointment.status]
                        }`}
                      >
                        {statusLabels[appointment.status]}
                      </Badge>
                      <span className="font-medium">
                        {appointment.start_time}
                      </span>
                    </div>
                    <div className="text-foreground font-medium">
                      {appointment.title}
                    </div>
                    {appointment.patient && (
                      <div className="text-muted-foreground">
                        {appointment.patient.name}
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  };

  const renderDayView = () => {
    const dayAppointments = getAppointmentsForDate(currentDate);

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5" />
            <span>
              {format(currentDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {dayAppointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                Nenhum agendamento para este dia
              </p>
            </div>
          ) : (
            dayAppointments.map((appointment) => (
              <div
                key={appointment.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">
                      {appointment.start_time} - {appointment.end_time}
                    </span>
                    <Badge
                      variant="outline"
                      className={statusColors[appointment.status]}
                    >
                      {statusLabels[appointment.status]}
                    </Badge>
                  </div>
                  <h3 className="font-semibold">{appointment.title}</h3>
                  {appointment.patient && (
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <User className="w-3 h-3" />
                      <span>{appointment.patient.name}</span>
                    </div>
                  )}
                  {appointment.description && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {appointment.description}
                    </p>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEdit(appointment)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Excluir agendamento</AlertDialogTitle>
                        <AlertDialogDescription>
                          Tem certeza que deseja excluir este agendamento? Esta
                          ação não pode ser desfeita.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(appointment.id)}
                        >
                          Excluir
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Agenda</h1>
          <p className="text-muted-foreground">
            Gerencie seus agendamentos e consultas
          </p>
        </div>
        <Dialog
          open={showAddDialog}
          onOpenChange={(open) => {
            setShowAddDialog(open);
            if (!open) {
              setEditingAppointment(null);
              resetForm();
            }
          }}
        >
          <DialogTrigger asChild>
            <Button className="bg-primary hover:bg-primary-hover">
              <Plus className="w-4 h-4 mr-2" />
              Novo Agendamento
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingAppointment ? "Editar Agendamento" : "Novo Agendamento"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Título *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) =>
                      setFormData({ ...formData, title: e.target.value })
                    }
                    placeholder="Ex: Consulta com paciente"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="patient">Paciente</Label>
                  <Select
                    value={formData.patient_id}
                    onValueChange={(value) =>
                      setFormData({ ...formData, patient_id: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um paciente" />
                    </SelectTrigger>
                    <SelectContent>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descrição</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Detalhes sobre o agendamento..."
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Data</Label>
                  <Input
                    type="date"
                    id="date"
                    value={formData.appointment_date}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        appointment_date: e.target.value,
                      })
                    }
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startTime">Hora de Início</Label>
                  <Input
                    type="time"
                    id="startTime"
                    value={formData.start_time}
                    onChange={(e) =>
                      setFormData({ ...formData, start_time: e.target.value })
                    }
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endTime">Hora de Fim</Label>
                  <Input
                    type="time"
                    id="endTime"
                    value={formData.end_time}
                    onChange={(e) =>
                      setFormData({ ...formData, end_time: e.target.value })
                    }
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: "scheduled") =>
                    setFormData({ ...formData, status: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Agendado</SelectItem>
                    <SelectItem value="completed">Concluído</SelectItem>
                    <SelectItem value="cancelled">Cancelado</SelectItem>
                    <SelectItem value="no_show">Falta</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddDialog(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit">
                  {editingAppointment ? "Atualizar" : "Criar"} Agendamento
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (viewMode === "week") {
                  setCurrentDate(subWeeks(currentDate, 1));
                } else {
                  setCurrentDate(subDays(currentDate, 1));
                }
              }}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date())}
            >
              Hoje
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (viewMode === "week") {
                  setCurrentDate(addWeeks(currentDate, 1));
                } else {
                  setCurrentDate(addDays(currentDate, 1));
                }
              }}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          <h2 className="text-xl font-semibold">
            {viewMode === "week"
              ? `${format(
                  startOfWeek(currentDate, { weekStartsOn: 1 }),
                  "dd MMM",
                  { locale: ptBR }
                )} - ${format(
                  addDays(startOfWeek(currentDate, { weekStartsOn: 1 }), 6),
                  "dd MMM yyyy",
                  { locale: ptBR }
                )}`
              : format(currentDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "day" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("day")}
          >
            Dia
          </Button>
          <Button
            variant={viewMode === "week" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("week")}
          >
            Semana
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">Carregando agendamentos...</p>
        </div>
      ) : viewMode === "week" ? (
        renderWeekView()
      ) : (
        renderDayView()
      )}
    </div>
  );
}
