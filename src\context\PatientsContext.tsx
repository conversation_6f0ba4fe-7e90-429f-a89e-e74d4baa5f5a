import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "./AuthContext";
import { useToast } from "@/hooks/use-toast";

export interface Patient {
  id: string;
  name: string;
  cpf: string;
  phone: string;
  mainComplaints: string;
  anamnesis: string;
  consultationSummaries: ConsultationSummary[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface ConsultationSummary {
  id: string;
  date: Date;
  summary: string;
  patientId: string;
}

interface PatientsContextType {
  patients: Patient[];
  loading: boolean;
  addPatient: (
    patient: Omit<
      Patient,
      "id" | "createdAt" | "updatedAt" | "userId" | "consultationSummaries"
    >
  ) => Promise<void>;
  updatePatient: (
    id: string,
    patient: Partial<
      Omit<
        Patient,
        "id" | "createdAt" | "updatedAt" | "userId" | "consultationSummaries"
      >
    >
  ) => Promise<void>;
  deletePatient: (id: string) => Promise<void>;
  getPatient: (id: string) => Patient | undefined;
  addConsultation: (
    patientId: string,
    consultation: Omit<ConsultationSummary, "id" | "patientId">
  ) => Promise<void>;
  updateConsultation: (
    consultationId: string,
    updates: Partial<Omit<ConsultationSummary, "id" | "patientId">>
  ) => Promise<void>;
  deleteConsultation: (consultationId: string) => Promise<void>;
  refreshPatients: () => Promise<void>;
}

const PatientsContext = createContext<PatientsContextType | undefined>(
  undefined
);

export function PatientsProvider({ children }: { children: ReactNode }) {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchPatients = async () => {
    if (!user) {
      setPatients([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      const { data: patientsData, error: patientsError } = await supabase
        .from("patients")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (patientsError) throw patientsError;

      const patientIds = patientsData?.map((p) => p.id) || [];
      const { data: consultationsData, error: consultationsError } =
        await supabase
          .from("consultation_summaries")
          .select("*")
          .in("patient_id", patientIds)
          .order("date", { ascending: false });

      if (consultationsError) throw consultationsError;
      const patientsWithConsultations =
        patientsData?.map((patient) => ({
          id: patient.id,
          name: patient.name,
          cpf: patient.cpf,
          phone: patient.phone,
          mainComplaints: patient.main_complaints || "",
          anamnesis: patient.anamnesis || "",
          userId: patient.user_id,
          createdAt: new Date(patient.created_at),
          updatedAt: new Date(patient.updated_at),
          consultationSummaries:
            consultationsData
              ?.filter((consultation) => consultation.patient_id === patient.id)
              .map((consultation) => ({
                id: consultation.id,
                date: new Date(consultation.date),
                summary: consultation.summary,
                patientId: consultation.patient_id,
              })) || [],
        })) || [];

      setPatients(patientsWithConsultations);
    } catch (error) {
      console.error("Error fetching patients:", error);
      toast({
        title: "Erro",
        description: "Erro ao carregar pacientes",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPatients();
  }, [user]);

  const addPatient = async (
    patientData: Omit<
      Patient,
      "id" | "createdAt" | "updatedAt" | "userId" | "consultationSummaries"
    >
  ) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("patients")
        .insert({
          user_id: user.id,
          name: patientData.name,
          cpf: patientData.cpf,
          phone: patientData.phone,
          main_complaints: patientData.mainComplaints,
          anamnesis: patientData.anamnesis,
        })
        .select()
        .single();

      if (error) throw error;

      const newPatient: Patient = {
        id: data.id,
        name: data.name,
        cpf: data.cpf,
        phone: data.phone,
        mainComplaints: data.main_complaints || "",
        anamnesis: data.anamnesis || "",
        userId: data.user_id,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        consultationSummaries: [],
      };

      setPatients((prev) => [newPatient, ...prev]);

      toast({
        title: "Sucesso",
        description: "Paciente adicionado com sucesso",
      });
    } catch (error) {
      console.error("Error adding patient:", error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar paciente",
        variant: "destructive",
      });
    }
  };

  const updatePatient = async (
    id: string,
    updates: Partial<
      Omit<
        Patient,
        "id" | "createdAt" | "updatedAt" | "userId" | "consultationSummaries"
      >
    >
  ) => {
    try {
      const { error } = await supabase
        .from("patients")
        .update({
          name: updates.name,
          cpf: updates.cpf,
          phone: updates.phone,
          main_complaints: updates.mainComplaints,
          anamnesis: updates.anamnesis,
        })
        .eq("id", id);

      if (error) throw error;

      setPatients((prev) =>
        prev.map((patient) =>
          patient.id === id
            ? {
                ...patient,
                ...updates,
                updatedAt: new Date(),
              }
            : patient
        )
      );

      toast({
        title: "Sucesso",
        description: "Paciente atualizado com sucesso",
      });
    } catch (error) {
      console.error("Error updating patient:", error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar paciente",
        variant: "destructive",
      });
    }
  };

  const deletePatient = async (id: string) => {
    try {
      const { error } = await supabase.from("patients").delete().eq("id", id);

      if (error) throw error;

      setPatients((prev) => prev.filter((patient) => patient.id !== id));

      toast({
        title: "Sucesso",
        description: "Paciente removido com sucesso",
      });
    } catch (error) {
      console.error("Error deleting patient:", error);
      toast({
        title: "Erro",
        description: "Erro ao remover paciente",
        variant: "destructive",
      });
    }
  };

  const getPatient = (id: string) => {
    return patients.find((patient) => patient.id === id);
  };

  const addConsultation = async (
    patientId: string,
    consultationData: Omit<ConsultationSummary, "id" | "patientId">
  ) => {
    try {
      const { data, error } = await supabase
        .from("consultation_summaries")
        .insert({
          patient_id: patientId,
          date: consultationData.date.toISOString().split("T")[0], // Convert to YYYY-MM-DD format
          summary: consultationData.summary,
        })
        .select()
        .single();

      if (error) throw error;

      const newConsultation: ConsultationSummary = {
        id: data.id,
        date: new Date(data.date),
        summary: data.summary,
        patientId: data.patient_id,
      };

      setPatients((prev) =>
        prev.map((patient) =>
          patient.id === patientId
            ? {
                ...patient,
                consultationSummaries: [
                  newConsultation,
                  ...patient.consultationSummaries,
                ].sort((a, b) => b.date.getTime() - a.date.getTime()),
                updatedAt: new Date(),
              }
            : patient
        )
      );

      toast({
        title: "Sucesso",
        description: "Consulta adicionada com sucesso",
      });
    } catch (error) {
      console.error("Error adding consultation:", error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar consulta",
        variant: "destructive",
      });
    }
  };

  const updateConsultation = async (
    consultationId: string,
    updates: Partial<Omit<ConsultationSummary, "id" | "patientId">>
  ) => {
    try {
      const updateData: any = {};
      if (updates.date) {
        updateData.date = updates.date.toISOString().split("T")[0];
      }
      if (updates.summary !== undefined) {
        updateData.summary = updates.summary;
      }

      const { error } = await supabase
        .from("consultation_summaries")
        .update(updateData)
        .eq("id", consultationId);

      if (error) throw error;

      setPatients((prev) =>
        prev.map((patient) => ({
          ...patient,
          consultationSummaries: patient.consultationSummaries.map(
            (consultation) =>
              consultation.id === consultationId
                ? { ...consultation, ...updates }
                : consultation
          ),
        }))
      );

      toast({
        title: "Sucesso",
        description: "Consulta atualizada com sucesso",
      });
    } catch (error) {
      console.error("Error updating consultation:", error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar consulta",
        variant: "destructive",
      });
    }
  };

  const deleteConsultation = async (consultationId: string) => {
    try {
      const { error } = await supabase
        .from("consultation_summaries")
        .delete()
        .eq("id", consultationId);

      if (error) throw error;

      setPatients((prev) =>
        prev.map((patient) => ({
          ...patient,
          consultationSummaries: patient.consultationSummaries.filter(
            (consultation) => consultation.id !== consultationId
          ),
        }))
      );

      toast({
        title: "Sucesso",
        description: "Consulta excluída com sucesso",
      });
    } catch (error) {
      console.error("Error deleting consultation:", error);
      toast({
        title: "Erro",
        description: "Erro ao excluir consulta",
        variant: "destructive",
      });
    }
  };

  const refreshPatients = async () => {
    await fetchPatients();
  };

  return (
    <PatientsContext.Provider
      value={{
        patients,
        loading,
        addPatient,
        updatePatient,
        deletePatient,
        getPatient,
        addConsultation,
        updateConsultation,
        deleteConsultation,
        refreshPatients,
      }}
    >
      {children}
    </PatientsContext.Provider>
  );
}

export function usePatients() {
  const context = useContext(PatientsContext);
  if (context === undefined) {
    throw new Error("usePatients must be used within a PatientsProvider");
  }
  return context;
}
